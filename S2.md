# 拍照搜题 API 服务开发计划

## 1. 项目概述
本项目旨在开发一款基于 Golang 的 Web API 服务，主要功能是提供拍照搜题及相关业务的接口服务。该服务将采用模块化设计，包含用户管理、应用管理、鉴权流控、模型服务等核心功能模块。

## 2. 技术栈选择
### 基础框架与组件
- **语言**: Golang 1.20+
- **Web框架**: Gin
- **数据库**: MySQL 8.0
- **缓存**: Redis 7.0+
- **ORM**: GORM
- **日志**: zap + lumberjack
- **配置管理**: viper
- **API文档**: Swagger
- **依赖注入**: wire
- **限流**: rate
- **消息队列**: RabbitMQ (可选，用于异步任务处理)

### 项目结构建议
```
├── cmd/                  # 应用程序入口
├── config/               # 配置文件
├── internal/             # 私有应用和库代码
│   ├── api/              # API 层
│   ├── middleware/       # 中间件
│   ├── model/            # 数据模型
│   ├── repository/       # 数据访问层
│   ├── service/          # 业务逻辑层
│   └── utils/            # 工具函数
├── pkg/                  # 可以被外部应用使用的库代码
├── scripts/              # 构建、安装、分析等脚本
└── test/                 # 测试文件
```

## 3. 核心模块详细设计

### 3.1 用户注册与账户模块
#### 功能需求
- 通过手机号 + 验证码 + 密码 + 邀请码完成注册
- 邀请码作为注册口令，存储于 config 配置表中，可由管理员修改
- 用户账号信息需唯一，手机号不可重复
- 成功注册后初始化账户余额字段（默认值为 0）

#### 数据模型设计
```go
// User 用户表
type User struct {
    ID           uint      `gorm:"primaryKey"`
    Phone        string    `gorm:"uniqueIndex;size:20"`
    Password     string    `gorm:"size:100"` // 存储加密后的密码
    Balance      float64   `gorm:"default:0"`
    Status       int       `gorm:"default:1"` // 1:正常 2:冻结
    CreatedAt    time.Time
    UpdatedAt    time.Time
    DeletedAt    gorm.DeletedAt `gorm:"index"`
}
```

#### API 接口设计
- `POST /api/v1/user/register` - 用户注册
- `POST /api/v1/user/login` - 用户登录
- `GET /api/v1/user/profile` - 获取用户信息
- `PUT /api/v1/user/profile` - 更新用户信息
- `POST /api/v1/user/send-code` - 发送验证码

### 3.2 应用管理模块
#### 功能需求
- 每个用户最多可创建 N 个应用，创建时填写名称并选择业务类型
- 自动生成 app_key / secret_key 作为调用凭证
- 应用状态支持：正常、冻结
- 冻结后不可调用 API；secret_key 可重置
- 用户可修改应用名称，不能删除或变更 type

#### 数据模型设计
```go
// Application 应用表
type Application struct {
    ID         uint      `gorm:"primaryKey"`
    UserID     uint      `gorm:"index"`
    Name       string    `gorm:"size:50"`
    Type       int       `gorm:"comment:'业务类型 1:拍照搜题'"`
    AppKey     string    `gorm:"uniqueIndex;size:32"`
    SecretKey  string    `gorm:"size:64"`
    Status     int       `gorm:"default:1;comment:'1:正常 2:冻结'"`
    CreatedAt  time.Time
    UpdatedAt  time.Time
    DeletedAt  gorm.DeletedAt `gorm:"index"`
}
```

#### API 接口设计
- `POST /api/v1/app` - 创建应用
- `GET /api/v1/app` - 获取应用列表
- `GET /api/v1/app/:id` - 获取应用详情
- `PUT /api/v1/app/:id` - 更新应用信息
- `PUT /api/v1/app/:id/reset-secret` - 重置 SecretKey
- `PUT /api/v1/app/:id/status` - 修改应用状态

### 3.3 鉴权与流控模块
#### 功能需求
- 请求携带 app_key 与 secret_key 进行校验
- 根据 app_key 识别绑定业务类型（type）
- 校验请求频率（默认 10 次/秒）
- 校验用户余额是否足够执行当前业务
- 校验参数合法性（如 type=1 需 img_url）

#### 实现方案
- 使用 Gin 中间件实现鉴权和流控逻辑
- 使用 Redis 实现分布式限流
- JWT 或 API Key 认证机制

#### 代码结构
```go
// middleware/auth.go - 鉴权中间件
// middleware/ratelimit.go - 限流中间件
// middleware/param_validator.go - 参数校验中间件
```

### 3.4 模型服务配置模块
#### 功能需求
- 系统支持配置多个模型的调用参数（如 prompt、top_p 等）
- 支持动态加载与在线修改
- 模型名称作为主键，如 qwen-vl-plus / deepseek-chat

#### 数据模型设计
```go
// ModelConfig 模型配置表
type ModelConfig struct {
    ID         uint      `gorm:"primaryKey"`
    Name       string    `gorm:"uniqueIndex;size:50"`
    ApiUrl     string    `gorm:"size:255"`
    ApiKey     string    `gorm:"size:100"`
    Params     string    `gorm:"type:text;comment:'JSON格式的参数配置'"`
    Status     int       `gorm:"default:1;comment:'1:启用 2:禁用'"`
    CreatedAt  time.Time
    UpdatedAt  time.Time
}
```

#### API 接口设计
- `GET /api/v1/admin/model` - 获取模型配置列表
- `GET /api/v1/admin/model/:id` - 获取模型配置详情
- `POST /api/v1/admin/model` - 创建模型配置
- `PUT /api/v1/admin/model/:id` - 更新模型配置
- `DELETE /api/v1/admin/model/:id` - 删除模型配置

### 3.5 拍照搜题业务（type = 1）
#### 功能需求流程
- 校验 img_url 是否有效
- 调用 qwen-vl-plus 获取题目结构
- 对返回结构体构造缓存 key 查询 Redis
- Redis 未命中 → 查 MySQL
- MySQL 未命中 → 调用 deepseek-chat 继续补全
- 格式化后写入 MySQL / Redis，返回结果给用户

#### 数据模型设计
```go
// Question 题目表
type Question struct {
    ID           uint      `gorm:"primaryKey"`
    Hash         string    `gorm:"uniqueIndex;size:64;comment:'题目哈希值'"`
    Content      string    `gorm:"type:text;comment:'题目内容'"`
    Analysis     string    `gorm:"type:text;comment:'题目解析'"`
    Answer       string    `gorm:"type:text;comment:'题目答案'"`
    Subject      string    `gorm:"size:20;comment:'学科'"`
    Grade        string    `gorm:"size:20;comment:'年级'"`
    Difficulty   int       `gorm:"comment:'难度 1-5'"`
    SourceModel  string    `gorm:"size:50;comment:'来源模型'"`
    CreatedAt    time.Time
    UpdatedAt    time.Time
}
```

#### API 接口设计
- `POST /api/v1/question/search` - 拍照搜题接口

### 3.6 数据缓存与持久化模块
#### 功能需求
- Redis 用于结构化题目的缓存（key 为哈希构造）
- Redis 内容 TTL 默认 7 天
- MySQL 为最终数据归档与查询依据

#### 实现方案
- 使用 Redis 的 Hash 结构存储题目数据
- 设计合理的缓存键生成策略
- 实现缓存更新和失效机制

#### 代码结构
```go
// repository/cache/question.go - 题目缓存操作
// repository/db/question.go - 题目数据库操作
```

### 3.7 日志记录与统计分析模块
#### 功能需求
- 记录用户所有请求行为，包括成功/失败
- 模型调用日志（是否命中缓存、调用时间、来源模型）
- 请求次数统计 / 应用访问分布 / 用户使用趋势

#### 数据模型设计
```go
// RequestLog 请求日志表
type RequestLog struct {
    ID           uint      `gorm:"primaryKey"`
    UserID       uint      `gorm:"index"`
    AppID        uint      `gorm:"index"`
    Path         string    `gorm:"size:100"`
    Method       string    `gorm:"size:10"`
    Params       string    `gorm:"type:text"`
    Response     string    `gorm:"type:text"`
    Status       int       `gorm:"comment:'HTTP状态码'"`
    Cost         int       `gorm:"comment:'耗时(ms)'"`
    IP           string    `gorm:"size:50"`
    CreatedAt    time.Time
}

// ModelCallLog 模型调用日志表
type ModelCallLog struct {
    ID           uint      `gorm:"primaryKey"`
    UserID       uint      `gorm:"index"`
    AppID        uint      `gorm:"index"`
    ModelName    string    `gorm:"size:50"`
    CacheHit     bool      `gorm:"comment:'是否命中缓存'"`
    Cost         int       `gorm:"comment:'耗时(ms)'"`
    Success      bool      `gorm:"comment:'是否成功'"`
    CreatedAt    time.Time
}
```

#### API 接口设计
- `GET /api/v1/admin/stats/users` - 用户统计
- `GET /api/v1/admin/stats/apps` - 应用统计
- `GET /api/v1/admin/stats/requests` - 请求统计
- `GET /api/v1/admin/stats/models` - 模型调用统计

### 3.8 计费与账户扣费模块
#### 功能需求
- 用户账户统一余额（所有 APP 共用）
- 每次请求前需预检查余额
- 成功才扣费，失败请求不计费
- 支持系统默认价格 + 用户定制价格

#### 数据模型设计
```go
// PriceConfig 价格配置表
type PriceConfig struct {
    ID           uint      `gorm:"primaryKey"`
    ServiceType  int       `gorm:"comment:'服务类型 1:拍照搜题'"`
    UserID       uint      `gorm:"default:0;comment:'0表示系统默认价格'"`
    Price        float64   `gorm:"comment:'单次调用价格'"`
    CreatedAt    time.Time
    UpdatedAt    time.Time
}

// BalanceLog 余额变动日志表
type BalanceLog struct {
    ID           uint      `gorm:"primaryKey"`
    UserID       uint      `gorm:"index"`
    Amount       float64   `gorm:"comment:'变动金额'"`
    Balance      float64   `gorm:"comment:'变动后余额'"`
    Type         int       `gorm:"comment:'类型 1:充值 2:消费 3:退款'"`
    Description  string    `gorm:"size:255"`
    RelatedID    uint      `gorm:"comment:'关联ID'"`
    CreatedAt    time.Time
}
```

#### API 接口设计
- `POST /api/v1/user/recharge` - 用户充值
- `GET /api/v1/user/balance-logs` - 获取余额变动记录
- `GET /api/v1/admin/price` - 获取价格配置
- `POST /api/v1/admin/price` - 设置价格配置

### 3.9 系统配置与后台管理模块
#### 功能需求
- 模型参数配置接口（支持修改）
- 用户价格配置接口
- 应用冻结/恢复控制接口
- 系统邀请注册口令设置接口
- 所有接口需管理员权限

#### 数据模型设计
```go
// SystemConfig 系统配置表
type SystemConfig struct {
    ID           uint      `gorm:"primaryKey"`
    Key          string    `gorm:"uniqueIndex;size:50"`
    Value        string    `gorm:"type:text"`
    Description  string    `gorm:"size:255"`
    CreatedAt    time.Time
    UpdatedAt    time.Time
}

// Admin 管理员表
type Admin struct {
    ID           uint      `gorm:"primaryKey"`
    Username     string    `gorm:"uniqueIndex;size:50"`
    Password     string    `gorm:"size:100"`
    Role         int       `gorm:"comment:'角色 1:超级管理员 2:普通管理员'"`
    CreatedAt    time.Time
    UpdatedAt    time.Time
    DeletedAt    gorm.DeletedAt `gorm:"index"`
}
```

#### API 接口设计
- `POST /api/v1/admin/login` - 管理员登录
- `GET /api/v1/admin/config` - 获取系统配置
- `PUT /api/v1/admin/config` - 更新系统配置
- `GET /api/v1/admin/users` - 获取用户列表
- `PUT /api/v1/admin/users/:id/status` - 修改用户状态

## 4. 开发计划与里程碑

### 阶段一：基础架构搭建（2周）
- 项目初始化与目录结构设计
- 数据库设计与迁移脚本编写
- 基础中间件开发（日志、错误处理、鉴权）
- 配置管理系统实现

### 阶段二：核心模块开发（4周）
- 用户注册与账户模块
- 应用管理模块
- 鉴权与流控模块
- 模型服务配置模块

### 阶段三：业务功能实现（3周）
- 拍照搜题业务处理模块
- 数据缓存与存储模块
- 计费与账户扣费模块

### 阶段四：管理功能与统计分析（2周）
- 系统配置与后台管理模块
- 日志记录与统计分析模块

### 阶段五：测试与优化（2周）
- 单元测试与集成测试
- 性能测试与优化
- 安全审计与漏洞修复

## 5. 部署与运维建议

### 部署架构
- 使用 Docker 容器化部署
- Nginx 作为反向代理和负载均衡
- 考虑使用 Kubernetes 进行容器编排
- 数据库主从复制，考虑读写分离

### 监控与告警
- Prometheus + Grafana 监控系统
- 接入 ELK 日志分析系统
- 设置关键指标告警机制

### 安全措施
- 使用 HTTPS 加密传输
- 实现 API 请求签名机制
- 敏感数据加密存储
- 定期安全审计与漏洞扫描

## 6. 扩展性考虑
- 设计支持水平扩展的架构
- 考虑未来可能的新业务类型接入
- 预留接口版本控制机制
- 设计模块化的代码结构，便于功能扩展