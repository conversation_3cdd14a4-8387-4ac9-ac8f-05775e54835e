# 拍照搜题API - 前端开发接口文档

## 文档概述

本文档专为前端开发团队提供，包含完整的API接口规范、请求示例、响应格式和错误处理说明。

### 基础信息

- **API版本**: v1
- **Base URL**: `http://your-domain.com` (生产环境)
- **Base URL**: `http://localhost:8080` (开发环境)
- **数据格式**: JSON
- **字符编码**: UTF-8
- **请求头**: `Content-Type: application/json`

### 统一响应格式

所有API接口都使用统一的响应格式：

```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

#### 响应字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| code | int | 状态码，200表示成功 |
| message | string | 响应消息 |
| data | object/array/null | 响应数据，成功时包含具体数据，失败时为null |

#### 状态码说明

| 状态码 | 说明 | 前端处理建议 |
|--------|------|-------------|
| 200 | 成功 | 正常处理数据 |
| 400 | 请求参数错误 | 显示错误信息，检查表单验证 |
| 401 | 未授权访问 | 跳转到登录页面 |
| 403 | 禁止访问 | 显示权限不足提示 |
| 404 | 资源不存在 | 显示404页面或提示 |
| 409 | 资源冲突 | 显示冲突信息（如手机号已注册） |
| 429 | 请求过于频繁 | 显示限流提示，禁用按钮一段时间 |
| 500 | 服务器内部错误 | 显示通用错误提示 |
| 503 | 服务不可用 | 显示服务维护提示 |

## 用户管理接口

### 1. 发送验证码

**接口地址**: `POST /api/v1/user/send-code`

**接口描述**: 向指定手机号发送验证码，用于用户注册

**请求参数**:

```json
{
  "phone": "13800138000"
}
```

| 参数名 | 类型 | 必填 | 长度 | 说明 |
|--------|------|------|------|------|
| phone | string | 是 | 11 | 手机号，必须是11位数字且以1开头 |

**请求示例**:

```javascript
// 使用fetch
fetch('/api/v1/user/send-code', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    phone: '13800138000'
  })
})
.then(response => response.json())
.then(data => {
  if (data.code === 200) {
    // 发送成功，启动倒计时
    console.log('验证码发送成功');
  } else {
    // 显示错误信息
    console.error(data.message);
  }
});

// 使用axios
axios.post('/api/v1/user/send-code', {
  phone: '13800138000'
})
.then(response => {
  const { code, message } = response.data;
  if (code === 200) {
    // 发送成功处理
  }
})
.catch(error => {
  // 错误处理
});
```

**成功响应**:

```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": null
}
```

**错误响应**:

```json
{
  "code": 400,
  "message": "手机号格式不正确"
}
```

```json
{
  "code": 429,
  "message": "验证码发送过于频繁，请稍后再试"
}
```

**前端处理建议**:
- 发送成功后启动60秒倒计时，期间禁用发送按钮
- 显示"验证码已发送到 138****8000"
- 错误时显示具体错误信息

### 2. 用户注册

**接口地址**: `POST /api/v1/user/register`

**接口描述**: 用户注册，需要手机号、验证码、密码和邀请码

**请求参数**:

```json
{
  "phone": "13800138000",
  "password": "123456",
  "code": "123456",
  "invite_code": "SOLVE2024"
}
```

| 参数名 | 类型 | 必填 | 长度 | 说明 |
|--------|------|------|------|------|
| phone | string | 是 | 11 | 手机号 |
| password | string | 是 | 6-20 | 密码，6-20位字符 |
| code | string | 是 | 6 | 验证码，6位数字 |
| invite_code | string | 是 | - | 邀请码，默认为"SOLVE2024" |

**请求示例**:

```javascript
// 注册表单提交
const registerUser = async (formData) => {
  try {
    const response = await fetch('/api/v1/user/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      // 注册成功，跳转到登录页或主页
      console.log('注册成功', result.data);
      return { success: true, user: result.data };
    } else {
      // 显示错误信息
      return { success: false, message: result.message };
    }
  } catch (error) {
    return { success: false, message: '网络错误，请重试' };
  }
};
```

**成功响应**:

```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 0,
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:

```json
{
  "code": 400,
  "message": "手机号格式不正确"
}
```

```json
{
  "code": 409,
  "message": "手机号已注册"
}
```

### 3. 用户登录

**接口地址**: `POST /api/v1/user/login`

**接口描述**: 用户登录，使用手机号和密码

**请求参数**:

```json
{
  "phone": "13800138000",
  "password": "123456"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| phone | string | 是 | 手机号 |
| password | string | 是 | 密码 |

**请求示例**:

```javascript
// 登录函数
const loginUser = async (phone, password) => {
  try {
    const response = await fetch('/api/v1/user/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ phone, password })
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      // 登录成功，保存用户信息
      localStorage.setItem('user', JSON.stringify(result.data));
      return { success: true, user: result.data };
    } else {
      return { success: false, message: result.message };
    }
  } catch (error) {
    return { success: false, message: '网络错误，请重试' };
  }
};
```

**成功响应**:

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 0,
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

**错误响应**:

```json
{
  "code": 401,
  "message": "用户名或密码错误"
}
```

```json
{
  "code": 403,
  "message": "账户已被冻结"
}
```

### 4. 获取用户信息

**接口地址**: `GET /api/v1/user/profile/{user_id}`

**接口描述**: 获取指定用户的详细信息

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID |

**请求示例**:

```javascript
// 获取用户信息
const getUserProfile = async (userId) => {
  try {
    const response = await fetch(`/api/v1/user/profile/${userId}`);
    const result = await response.json();
    
    if (result.code === 200) {
      return { success: true, user: result.data };
    } else {
      return { success: false, message: result.message };
    }
  } catch (error) {
    return { success: false, message: '获取用户信息失败' };
  }
};
```

**成功响应**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 0,
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### 5. 更新用户信息

**接口地址**: `PUT /api/v1/user/profile/{user_id}`

**接口描述**: 更新用户信息（目前支持修改密码）

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID |

**请求参数**:

```json
{
  "password": "newpassword123"
}
```

| 参数名 | 类型 | 必填 | 长度 | 说明 |
|--------|------|------|------|------|
| password | string | 否 | 6-20 | 新密码，6-20位字符 |

**请求示例**:

```javascript
// 修改密码
const updatePassword = async (userId, newPassword) => {
  try {
    const response = await fetch(`/api/v1/user/profile/${userId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ password: newPassword })
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      return { success: true, message: '密码修改成功' };
    } else {
      return { success: false, message: result.message };
    }
  } catch (error) {
    return { success: false, message: '修改失败，请重试' };
  }
};
```

**成功响应**:

```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "phone": "13800138000",
    "balance": 0,
    "status": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

## 系统接口

### 健康检查

**接口地址**: `GET /health`

**接口描述**: 检查服务健康状态

**请求示例**:

```javascript
// 健康检查
const checkHealth = async () => {
  try {
    const response = await fetch('/health');
    const result = await response.json();
    return result.status === 'ok';
  } catch (error) {
    return false;
  }
};
```

**响应示例**:

```json
{
  "status": "ok",
  "app": "solve_api",
  "version": "1.0.0"
}
```

## 数据模型

### 用户模型 (User)

```typescript
interface User {
  id: number;           // 用户ID
  phone: string;        // 手机号
  balance: number;      // 账户余额
  status: number;       // 用户状态：1-正常，2-冻结
  created_at: string;   // 创建时间 (ISO 8601格式)
  updated_at: string;   // 更新时间 (ISO 8601格式)
}
```

## 错误处理

### 统一错误处理函数

```javascript
// 统一的API请求函数
const apiRequest = async (url, options = {}) => {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    
    const result = await response.json();
    
    // 根据状态码处理
    switch (result.code) {
      case 200:
        return { success: true, data: result.data };
      case 401:
        // 未授权，跳转登录
        window.location.href = '/login';
        return { success: false, message: '请先登录' };
      case 429:
        // 限流
        return { success: false, message: '操作过于频繁，请稍后再试' };
      default:
        return { success: false, message: result.message || '操作失败' };
    }
  } catch (error) {
    console.error('API请求错误:', error);
    return { success: false, message: '网络错误，请检查网络连接' };
  }
};
```

### 表单验证

```javascript
// 手机号验证
const validatePhone = (phone) => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

// 密码验证
const validatePassword = (password) => {
  return password.length >= 6 && password.length <= 20;
};

// 验证码验证
const validateCode = (code) => {
  const codeRegex = /^\d{6}$/;
  return codeRegex.test(code);
};
```

## 前端集成建议

### 1. 状态管理

```javascript
// 用户状态管理 (使用 React Context 示例)
const UserContext = createContext();

export const UserProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(false);
  
  const login = async (phone, password) => {
    setLoading(true);
    const result = await loginUser(phone, password);
    if (result.success) {
      setUser(result.user);
    }
    setLoading(false);
    return result;
  };
  
  const logout = () => {
    setUser(null);
    localStorage.removeItem('user');
  };
  
  return (
    <UserContext.Provider value={{ user, login, logout, loading }}>
      {children}
    </UserContext.Provider>
  );
};
```

### 2. 请求拦截器

```javascript
// axios 拦截器示例
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      // 清除用户信息，跳转登录
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);
```

### 3. 组件示例

```jsx
// 发送验证码组件
const SendCodeButton = ({ phone, onSuccess }) => {
  const [countdown, setCountdown] = useState(0);
  const [loading, setLoading] = useState(false);
  
  const handleSendCode = async () => {
    if (!validatePhone(phone)) {
      alert('请输入正确的手机号');
      return;
    }
    
    setLoading(true);
    const result = await apiRequest('/api/v1/user/send-code', {
      method: 'POST',
      body: JSON.stringify({ phone })
    });
    
    setLoading(false);
    
    if (result.success) {
      setCountdown(60);
      onSuccess?.();
      // 启动倒计时
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      alert(result.message);
    }
  };
  
  return (
    <button 
      onClick={handleSendCode}
      disabled={loading || countdown > 0}
    >
      {loading ? '发送中...' : countdown > 0 ? `${countdown}s` : '发送验证码'}
    </button>
  );
};
```

## 注意事项

1. **验证码有效期**: 5分钟
2. **发送频率限制**: 同一手机号60秒内只能发送一次
3. **密码安全**: 前端不要明文存储密码
4. **用户状态**: 及时检查用户状态，冻结用户需要特殊处理
5. **错误提示**: 根据不同错误码显示友好的提示信息
6. **网络异常**: 做好网络异常的处理和重试机制

## 联系方式

如有接口问题或需要技术支持，请联系后端开发团队。
