#!/bin/bash

# 拍照搜题API开发测试脚本
# 用于快速启动、停止、重启服务，方便开发测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置
APP_NAME="solve_api"
PID_FILE="$APP_NAME.pid"
LOG_FILE="logs/dev.log"
CONFIG_FILE="config/config.yaml"
TEST_SCRIPT="./test_api.sh"

# 显示Logo
show_logo() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════╗"
    echo "║        拍照搜题 API 开发工具         ║"
    echo "║            Development Tool          ║"
    echo "╚══════════════════════════════════════╝"
    echo -e "${NC}"
}

# 显示帮助信息
show_help() {
    show_logo
    echo -e "${BLUE}使用方法: $0 [命令]${NC}"
    echo ""
    echo -e "${YELLOW}服务管理命令:${NC}"
    echo "  start     启动服务"
    echo "  stop      停止服务"
    echo "  restart   重启服务"
    echo "  status    查看服务状态"
    echo ""
    echo -e "${YELLOW}开发测试命令:${NC}"
    echo "  dev       开发模式启动（前台运行，实时日志）"
    echo "  test      运行API测试"
    echo "  logs      查看实时日志"
    echo "  clean     清理日志和临时文件"
    echo ""
    echo -e "${YELLOW}构建命令:${NC}"
    echo "  build     构建应用"
    echo "  deps      安装/更新依赖"
    echo ""
    echo -e "${YELLOW}其他命令:${NC}"
    echo "  health    检查服务健康状态"
    echo "  admin     检查管理员账号"
    echo "  db        测试数据库连接"
    echo "  help      显示帮助信息"
    echo ""
    echo -e "${PURPLE}快捷键组合:${NC}"
    echo "  Ctrl+C    停止前台运行的服务"
    echo ""
}

# 检查Go环境
check_go() {
    if ! command -v go &> /dev/null; then
        echo -e "${RED}错误: Go未安装或不在PATH中${NC}"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    echo -e "${BLUE}检查依赖...${NC}"
    check_go
    
    # 创建必要目录
    mkdir -p logs
    
    # 检查配置文件
    if [ ! -f "$CONFIG_FILE" ]; then
        echo -e "${RED}错误: 配置文件 $CONFIG_FILE 不存在${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 依赖检查完成${NC}"
}

# 获取进程PID
get_pid() {
    if [ -f "$PID_FILE" ]; then
        cat "$PID_FILE"
    else
        echo ""
    fi
}

# 检查进程是否运行
is_running() {
    local pid=$(get_pid)
    if [ -n "$pid" ] && ps -p "$pid" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 启动服务
start_service() {
    echo -e "${BLUE}启动 $APP_NAME 服务...${NC}"
    
    if is_running; then
        echo -e "${YELLOW}服务已经在运行 (PID: $(get_pid))${NC}"
        return 0
    fi
    
    check_dependencies
    
    echo -e "${BLUE}构建应用...${NC}"
    go build -o "$APP_NAME" cmd/main.go
    
    echo -e "${BLUE}启动服务...${NC}"
    nohup ./"$APP_NAME" > "$LOG_FILE" 2>&1 &
    local pid=$!
    echo "$pid" > "$PID_FILE"
    
    # 等待服务启动
    sleep 2
    
    if is_running; then
        echo -e "${GREEN}✓ 服务启动成功 (PID: $pid)${NC}"
        echo -e "${CYAN}日志文件: $LOG_FILE${NC}"
        
        # 检查健康状态
        sleep 1
        check_health_silent
    else
        echo -e "${RED}✗ 服务启动失败${NC}"
        rm -f "$PID_FILE"
        exit 1
    fi
}

# 停止服务
stop_service() {
    echo -e "${BLUE}停止 $APP_NAME 服务...${NC}"
    
    if ! is_running; then
        echo -e "${YELLOW}服务未运行${NC}"
        rm -f "$PID_FILE"
        return 0
    fi
    
    local pid=$(get_pid)
    echo -e "${BLUE}发送停止信号 (PID: $pid)${NC}"
    kill "$pid"
    
    # 等待进程结束
    for i in {1..10}; do
        if ! is_running; then
            break
        fi
        sleep 1
        echo -n "."
    done
    echo ""
    
    # 强制杀死进程
    if is_running; then
        echo -e "${YELLOW}强制停止进程${NC}"
        kill -9 "$pid"
    fi
    
    rm -f "$PID_FILE"
    echo -e "${GREEN}✓ 服务已停止${NC}"
}

# 重启服务
restart_service() {
    echo -e "${BLUE}重启 $APP_NAME 服务...${NC}"
    stop_service
    sleep 1
    start_service
}

# 开发模式启动
dev_mode() {
    echo -e "${BLUE}开发模式启动 $APP_NAME...${NC}"
    
    if is_running; then
        echo -e "${YELLOW}检测到服务正在后台运行，先停止...${NC}"
        stop_service
        sleep 1
    fi
    
    check_dependencies
    
    echo -e "${GREEN}✓ 前台启动服务（按 Ctrl+C 停止）${NC}"
    echo -e "${CYAN}实时日志输出:${NC}"
    echo "----------------------------------------"
    
    # 前台运行
    go run cmd/main.go
}

# 查看服务状态
show_status() {
    echo -e "${BLUE}$APP_NAME 服务状态:${NC}"
    
    if is_running; then
        local pid=$(get_pid)
        echo -e "${GREEN}状态: 运行中${NC}"
        echo -e "PID: $pid"
        echo -e "启动时间: $(ps -o lstart= -p "$pid" 2>/dev/null || echo "未知")"
        echo -e "内存使用: $(ps -o rss= -p "$pid" 2>/dev/null | awk '{print $1/1024 " MB"}' || echo "未知")"
        
        # 检查健康状态
        check_health_silent
    else
        echo -e "${RED}状态: 未运行${NC}"
        rm -f "$PID_FILE"
    fi
}

# 查看日志
show_logs() {
    echo -e "${BLUE}查看 $APP_NAME 实时日志:${NC}"
    echo -e "${CYAN}日志文件: $LOG_FILE${NC}"
    echo "----------------------------------------"
    
    if [ -f "$LOG_FILE" ]; then
        tail -f "$LOG_FILE"
    else
        echo -e "${YELLOW}日志文件不存在，等待日志输出...${NC}"
        touch "$LOG_FILE"
        tail -f "$LOG_FILE"
    fi
}

# 运行测试
run_test() {
    echo -e "${BLUE}运行API测试...${NC}"
    
    if ! is_running; then
        echo -e "${YELLOW}服务未运行，先启动服务...${NC}"
        start_service
        sleep 2
    fi
    
    if [ -f "$TEST_SCRIPT" ]; then
        echo -e "${GREEN}执行测试脚本: $TEST_SCRIPT${NC}"
        "$TEST_SCRIPT"
    else
        echo -e "${RED}测试脚本不存在: $TEST_SCRIPT${NC}"
        exit 1
    fi
}

# 构建应用
build_app() {
    echo -e "${BLUE}构建 $APP_NAME...${NC}"
    check_go
    
    echo -e "${BLUE}编译应用...${NC}"
    go build -o "$APP_NAME" cmd/main.go
    
    echo -e "${GREEN}✓ 构建完成: $APP_NAME${NC}"
    ls -lh "$APP_NAME"
}

# 安装依赖
install_deps() {
    echo -e "${BLUE}安装/更新依赖...${NC}"
    check_go
    
    echo -e "${BLUE}下载依赖...${NC}"
    go mod tidy
    go mod download
    
    echo -e "${GREEN}✓ 依赖安装完成${NC}"
}

# 清理文件
clean_files() {
    echo -e "${BLUE}清理临时文件...${NC}"
    
    # 停止服务
    if is_running; then
        echo -e "${YELLOW}停止运行中的服务...${NC}"
        stop_service
    fi
    
    # 清理文件
    rm -f "$APP_NAME"
    rm -f "$PID_FILE"
    rm -f "$LOG_FILE"
    rm -rf logs/*.log
    
    echo -e "${GREEN}✓ 清理完成${NC}"
}

# 检查健康状态（静默）
check_health_silent() {
    if curl -s http://localhost:8080/health > /dev/null 2>&1; then
        echo -e "${GREEN}健康状态: 正常${NC}"
        return 0
    else
        echo -e "${RED}健康状态: 异常${NC}"
        return 1
    fi
}

# 检查健康状态
check_health() {
    echo -e "${BLUE}检查服务健康状态...${NC}"

    if ! is_running; then
        echo -e "${RED}服务未运行${NC}"
        return 1
    fi

    echo -e "${BLUE}请求健康检查接口...${NC}"

    if curl -s http://localhost:8080/health; then
        echo ""
        echo -e "${GREEN}✓ 服务健康检查通过${NC}"
    else
        echo -e "${RED}✗ 服务健康检查失败${NC}"
        return 1
    fi
}

# 检查管理员账号
check_admin() {
    echo -e "${BLUE}检查管理员账号...${NC}"

    if [ -f "scripts/check_admin.sh" ]; then
        ./scripts/check_admin.sh
    else
        echo -e "${RED}管理员检查脚本不存在${NC}"
        return 1
    fi
}

# 测试数据库连接
test_database() {
    echo -e "${BLUE}测试数据库连接...${NC}"

    if [ -f "scripts/test_db.sh" ]; then
        ./scripts/test_db.sh
    else
        echo -e "${RED}数据库测试脚本不存在${NC}"
        return 1
    fi
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        dev)
            dev_mode
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        test)
            run_test
            ;;
        build)
            build_app
            ;;
        deps)
            install_deps
            ;;
        clean)
            clean_files
            ;;
        health)
            check_health
            ;;
        admin)
            check_admin
            ;;
        db)
            test_database
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo -e "${RED}错误: 未知命令 '$1'${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 捕获Ctrl+C信号
trap 'echo -e "\n${YELLOW}收到停止信号，正在退出...${NC}"; exit 0' INT

# 执行主函数
main "$@"
