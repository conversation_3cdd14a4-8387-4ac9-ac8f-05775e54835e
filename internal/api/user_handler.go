package api

import (
	"solve_api/internal/model"
	"solve_api/internal/service"
	"solve_api/internal/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

type UserHandler struct {
	userService *service.UserService
}

// NewUserHandler 创建用户处理器实例
func NewUserHandler(userService *service.UserService) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

// Register 用户注册
// @Summary 用户注册
// @Description 通过手机号、验证码、密码和邀请码完成用户注册
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body model.UserRegisterRequest true "注册请求参数"
// @Success 200 {object} utils.Response{data=model.UserResponse} "注册成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 409 {object} utils.Response "手机号已注册"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/user/register [post]
func (h *UserHandler) Register(c *gin.Context) {
	var req model.UserRegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	user, err := h.userService.Register(&req)
	if err != nil {
		if err.Error() == "手机号已注册" {
			utils.Conflict(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "注册成功", user)
}

// Login 用户登录
// @Summary 用户登录
// @Description 通过手机号和密码进行用户登录
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body model.UserLoginRequest true "登录请求参数"
// @Success 200 {object} utils.Response{data=model.UserResponse} "登录成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 401 {object} utils.Response "用户名或密码错误"
// @Failure 403 {object} utils.Response "账户已被冻结"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/user/login [post]
func (h *UserHandler) Login(c *gin.Context) {
	var req model.UserLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	user, err := h.userService.Login(&req)
	if err != nil {
		switch err.Error() {
		case "用户不存在", "密码错误":
			utils.Unauthorized(c, "用户名或密码错误")
		case "账户已被冻结":
			utils.Forbidden(c, err.Error())
		default:
			utils.ServerError(c, err.Error())
		}
		return
	}

	utils.SuccessWithMessage(c, "登录成功", user)
}

// GetProfile 获取用户信息
// @Summary 获取用户信息
// @Description 获取当前登录用户的详细信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param user_id path int true "用户ID"
// @Success 200 {object} utils.Response{data=model.UserResponse} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "用户不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/user/profile/{user_id} [get]
func (h *UserHandler) GetProfile(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	user, err := h.userService.GetProfile(uint(userID))
	if err != nil {
		if err.Error() == "用户不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.Success(c, user)
}

// UpdateProfile 更新用户信息
// @Summary 更新用户信息
// @Description 更新当前登录用户的信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param user_id path int true "用户ID"
// @Param request body model.UserProfileUpdateRequest true "更新请求参数"
// @Success 200 {object} utils.Response{data=model.UserResponse} "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "用户不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/user/profile/{user_id} [put]
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	var req model.UserProfileUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	user, err := h.userService.UpdateProfile(uint(userID), &req)
	if err != nil {
		if err.Error() == "用户不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "更新成功", user)
}

// SendCode 发送验证码
// @Summary 发送验证码
// @Description 向指定手机号发送验证码
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body model.SendCodeRequest true "发送验证码请求参数"
// @Success 200 {object} utils.Response "发送成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 429 {object} utils.Response "发送过于频繁"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/user/send-code [post]
func (h *UserHandler) SendCode(c *gin.Context) {
	var req model.SendCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	err := h.userService.SendCode(&req)
	if err != nil {
		if err.Error() == "验证码发送过于频繁，请稍后再试" {
			utils.TooManyRequests(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "验证码发送成功", nil)
}
